# Development Dockerfile for Backend
FROM node:20-alpine

WORKDIR /app

# Install system dependencies for building native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    libc6-compat \
    curl \
    && ln -sf python3 /usr/bin/python

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies including dev dependencies
RUN npm ci

# Copy source code
COPY . .

# Set environment variables for development
ENV NODE_ENV=development
ENV PORT=3002
ENV HOSTNAME="0.0.0.0"

# Expose the port and debug port
EXPOSE 3002 9229

# Start the development server with hot reloading and debugging
CMD ["npm", "run", "start:debug"]
