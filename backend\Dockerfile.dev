# Development Dockerfile for Backend
FROM node:20-alpine

WORKDIR /app

# Install system dependencies for building native modules (especially for TensorFlow)
RUN apk add --no-cache \
    python3 \
    python3-dev \
    py3-pip \
    make \
    g++ \
    gcc \
    libc6-compat \
    curl \
    git \
    && ln -sf python3 /usr/bin/python

# Copy package files
COPY package.json package-lock.json* ./

# Remove package-lock.json if it exists (to avoid sync issues) and install fresh
RUN rm -f package-lock.json && npm install --legacy-peer-deps

# Copy source code
COPY . .

# Set environment variables for development
ENV NODE_ENV=development
ENV PORT=3002
ENV HOSTNAME="0.0.0.0"

# Expose the port and debug port
EXPOSE 3002 9229

# Start the development server with hot reloading and debugging
CMD ["npm", "run", "start:debug"]
