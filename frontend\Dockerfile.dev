# Development Dockerfile for Frontend
FROM node:20-alpine

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache libc6-compat

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies including dev dependencies
RUN npm ci

# Copy source code
COPY . .

# Set environment variables for development
ENV NODE_ENV=development
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV WATCHPACK_POLLING=true

# Expose the port
EXPOSE 3000

# Start the development server with hot reloading
CMD ["npm", "run", "dev"]
