# Frontend specific .dockerignore

# Dependencies
node_modules/
.npm
.pnpm-store

# Build outputs
.next/
out/
dist/
build/

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
.nyc_output
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Misc
.eslintcache
.cache
.parcel-cache

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore
