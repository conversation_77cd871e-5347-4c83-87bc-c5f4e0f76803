# Multi-stage Dockerfile for MedTrack Hub Monorepo
# This Dockerfile builds all services in a single container for simplified deployment

# Build stage for Node.js services
FROM node:20-alpine AS node-builder

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    libc6-compat \
    && ln -sf python3 /usr/bin/python

# Copy package files for both frontend and backend
COPY frontend/package.json frontend/package-lock.json* ./frontend/
COPY backend/package.json backend/package-lock.json* ./backend/
COPY package.json package-lock.json* ./

# Install root dependencies
RUN npm ci --only=production --ignore-scripts

# Install frontend dependencies
WORKDIR /app/frontend
RUN npm ci --only=production --ignore-scripts

# Install backend dependencies
WORKDIR /app/backend
RUN npm ci --only=production --ignore-scripts

# Copy source code and build frontend
WORKDIR /app/frontend
COPY frontend/ .
RUN npm run build

# Copy source code and build backend
WORKDIR /app/backend
COPY backend/ .
RUN npm run build

# Build stage for Python analytics
FROM python:3.11-slim AS python-builder

WORKDIR /app

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY backend/python_analytics/requirements.txt .
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir --upgrade pip setuptools wheel \
    && pip install --no-cache-dir -r requirements.txt

# Production stage
FROM node:20-alpine AS production

WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache \
    python3 \
    py3-pip \
    libc6-compat \
    wget \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root users
RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs --ingroup nodejs \
    && adduser --system --uid 1002 nestjs --ingroup nodejs \
    && addgroup --system --gid 1003 python \
    && adduser --system --uid 1003 analytics --ingroup python

# Copy Node.js applications from builder
COPY --from=node-builder --chown=nextjs:nodejs /app/frontend/.next/standalone ./frontend/
COPY --from=node-builder --chown=nextjs:nodejs /app/frontend/.next/static ./frontend/.next/static
COPY --from=node-builder --chown=nextjs:nodejs /app/frontend/public ./frontend/public
COPY --from=node-builder --chown=nestjs:nodejs /app/backend/dist ./backend/dist
COPY --from=node-builder --chown=nestjs:nodejs /app/backend/node_modules ./backend/node_modules
COPY --from=node-builder --chown=nestjs:nodejs /app/backend/package.json ./backend/

# Copy Python application and virtual environment
COPY --from=python-builder /opt/venv /opt/venv
COPY --chown=analytics:python backend/python_analytics/ ./analytics/

# Create startup script
COPY <<EOF /app/start.sh
#!/bin/sh
set -e

# Start analytics service in background
cd /app/analytics
export PATH="/opt/venv/bin:\$PATH"
su analytics -c "uvicorn main:app --host 0.0.0.0 --port 5000" &

# Start backend service in background
cd /app/backend
su nestjs -c "node dist/main" &

# Start frontend service in foreground
cd /app/frontend
su nextjs -c "node server.js"
EOF

RUN chmod +x /app/start.sh

# Set environment variables
ENV NODE_ENV=production \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:\$PATH"

# Health check for all services
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/api/health && \
      curl -f http://localhost:3002/api/health && \
      curl -f http://localhost:5000/health || exit 1

# Expose all ports
EXPOSE 3000 3002 5000

# Start all services
CMD ["/app/start.sh"]
