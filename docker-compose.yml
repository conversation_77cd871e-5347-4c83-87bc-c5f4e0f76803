version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:${BACKEND_PORT:-3002}
      - NEXT_PUBLIC_ANALYTICS_API_URL=http://localhost:${ANALYTICS_PORT:-5000}
      - NODE_ENV=${NODE_ENV:-development}
    depends_on:
      backend:
        condition: service_healthy
      analytics:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - medtrack-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "${BACKEND_PORT:-3002}:3002"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-medtrack}
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-change-in-production}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET:-your-jwt-refresh-secret-change-in-production}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis-password}@redis:6379
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3002
      - TYPEORM_SYNC=${TYPEORM_SYNC:-true}
      - TYPEORM_LOGGING=${TYPEORM_LOGGING:-true}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - medtrack-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  analytics:
    build:
      context: ./backend/python_analytics
      dockerfile: Dockerfile
    ports:
      - "${ANALYTICS_PORT:-5000}:5000"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-medtrack}
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-change-in-production}
      - PYTHONUNBUFFERED=1
      - PORT=5000
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - medtrack-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  db:
    image: postgres:16-alpine
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-medtrack}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - medtrack-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-medtrack}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis:
    image: redis:7-alpine
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis-password} --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - medtrack-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

networks:
  medtrack-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local