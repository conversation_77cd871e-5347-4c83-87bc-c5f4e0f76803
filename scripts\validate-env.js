const requiredEnvVars = {
  common: [
    'NODE_ENV',
    'FRONTEND_PORT',
    'BACKEND_PORT',
    'ANALYTICS_PORT'
  ],
  database: [
    'POSTGRES_HOST',
    'POSTGRES_PORT',
    'POSTGRES_USER',
    'POSTGRES_PASSWORD',
    'POSTGRES_DB'
  ],
  redis: [
    'REDIS_HOST',
    'REDIS_PORT',
    'REDIS_PASSWORD'
  ],
  security: [
    'JWT_SECRET',
    'JWT_REFRESH_SECRET'
  ],
  urls: [
    'NEXT_PUBLIC_API_URL',
    'NEXT_PUBLIC_ANALYTICS_API_URL'
  ]
};

function validateEnv() {
  const errors = [];
  
  for (const [category, vars] of Object.entries(requiredEnvVars)) {
    const missing = vars.filter(env => !process.env[env]);
    if (missing.length) {
      errors.push(`Missing ${category} environment variables: ${missing.join(', ')}`);
    }
  }

  // Additional validation for production
  if (process.env.NODE_ENV === 'production') {
    // Validate URL formats
    const urlVars = ['NEXT_PUBLIC_API_URL', 'NEXT_PUBLIC_ANALYTICS_API_URL'];
    urlVars.forEach(urlVar => {
      const url = process.env[urlVar];
      if (url && !url.startsWith('https://')) {
        errors.push(`${urlVar} must use HTTPS in production`);
      }
    });

    // Validate security requirements
    const jwtSecret = process.env.JWT_SECRET;
    if (jwtSecret && jwtSecret.length < 32) {
      errors.push('JWT_SECRET must be at least 32 characters long in production');
    }
  }

  if (errors.length) {
    console.error('Environment validation failed:');
    errors.forEach(error => console.error(`- ${error}`));
    process.exit(1);
  }

  console.log('Environment validation successful!');
}

// Run validation if this script is run directly
if (require.main === module) {
  validateEnv();
}

module.exports = validateEnv;
