version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:${BACKEND_PORT:-3002}
      - NEXT_PUBLIC_ANALYTICS_API_URL=http://localhost:${ANALYTICS_PORT:-5000}
      - NODE_ENV=development
      - WATCHPACK_POLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
      - analytics
    restart: unless-stopped
    networks:
      - medtrack-dev-network
    stdin_open: true
    tty: true

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "${BACKEND_PORT:-3002}:3002"
      - "9229:9229" # Debug port
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-medtrack}
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET:-dev-jwt-refresh-secret}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis-password}@redis:6379
      - NODE_ENV=development
      - PORT=3002
      - TYPEORM_SYNC=true
      - TYPEORM_LOGGING=true
    volumes:
      - ./backend:/app
      - /app/node_modules
      - /app/dist
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - medtrack-dev-network
    stdin_open: true
    tty: true

  analytics:
    build:
      context: ./backend/python_analytics
      dockerfile: Dockerfile.dev
    ports:
      - "${ANALYTICS_PORT:-5000}:5000"
      - "5678:5678" # Debug port for Python
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-medtrack}
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret}
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - PORT=5000
      - FLASK_ENV=development
      - PYTHONPATH=/app
    volumes:
      - ./backend/python_analytics:/app
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - medtrack-dev-network
    stdin_open: true
    tty: true

  db:
    image: postgres:16-alpine
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-medtrack}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - medtrack-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-medtrack}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis:
    image: redis:7-alpine
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis-password} --appendonly yes
    volumes:
      - redis_dev_data:/data
    restart: unless-stopped
    networks:
      - medtrack-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # Development tools
  adminer:
    image: adminer:4.8.1
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=db
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - medtrack-dev-network

  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:${REDIS_PASSWORD:-redis-password}
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - medtrack-dev-network

networks:
  medtrack-dev-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
