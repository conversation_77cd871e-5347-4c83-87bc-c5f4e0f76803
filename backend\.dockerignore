# Backend specific .dockerignore

# Dependencies
node_modules/
.npm
.pnpm-store

# Build outputs
dist/
build/

# Development files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
.nyc_output
test/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Misc
.eslintcache
.cache

# TypeScript
*.tsbuildinfo

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Python analytics (exclude from backend build)
python_analytics/
