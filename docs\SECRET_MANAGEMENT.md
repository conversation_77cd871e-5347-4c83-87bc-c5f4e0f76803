# 🔐 Secret Management Guide

## ⚠️ **CRITICAL: Where to Input Actual Passwords/Secrets**

### 🚨 **NEVER PUT SECRETS IN THESE FILES** (Already secured ✅)
- ❌ `package.json` files
- ❌ `Dockerfile` files  
- ❌ `docker-compose.yml` files
- ❌ Source code files
- ❌ Configuration files committed to Git

### ✅ **WHERE TO PUT ACTUAL SECRETS**

## 1. **Local Development Environment**

### **File: `.env` (Root directory)**
```bash
# Copy from .env.example and fill in real values
cp .env.example .env

# Edit .env with actual secrets:
NODE_ENV=development

# Database (Use your actual database credentials)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=your_actual_db_user
POSTGRES_PASSWORD=your_actual_strong_password_here
POSTGRES_DB=medtrack

# Redis (Use your actual Redis password)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_actual_redis_password_here

# JWT Secrets (Generate strong 32+ character secrets)
JWT_SECRET=your_actual_jwt_secret_32_chars_minimum_here
JWT_REFRESH_SECRET=your_actual_refresh_secret_32_chars_minimum

# Email (Your actual email service credentials)
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_actual_app_password_here

# AWS (If using AWS services)
AWS_ACCESS_KEY_ID=your_actual_aws_access_key
AWS_SECRET_ACCESS_KEY=your_actual_aws_secret_key
AWS_S3_BUCKET=your_actual_bucket_name
```

### **File: `frontend/.env.local`**
```bash
# Frontend environment variables
NEXT_PUBLIC_API_URL=http://localhost:3002
NEXT_PUBLIC_ANALYTICS_API_URL=http://localhost:5000

# These are public (NEXT_PUBLIC_*) so they're safe to set
```

### **File: `backend/.env`**
```bash
# Backend-specific environment variables
NODE_ENV=development
PORT=3002

# Database connection
DATABASE_URL=postgresql://your_user:your_password@localhost:5432/medtrack

# JWT secrets (same as root .env)
JWT_SECRET=your_actual_jwt_secret_32_chars_minimum_here
JWT_REFRESH_SECRET=your_actual_refresh_secret_32_chars_minimum

# Redis connection
REDIS_URL=redis://:your_password@localhost:6379
```

### **File: `backend/python_analytics/.env`**
```bash
# Python analytics environment variables
DATABASE_URL=postgresql://your_user:your_password@localhost:5432/medtrack
JWT_SECRET=your_actual_jwt_secret_32_chars_minimum_here
PORT=5000
PYTHONUNBUFFERED=1
```

## 2. **Production Environment**

### **Option A: Environment Variables (Recommended)**
Set these directly in your production environment:

```bash
# On your production server
export POSTGRES_PASSWORD="your_production_db_password"
export JWT_SECRET="your_production_jwt_secret_64_chars_recommended"
export JWT_REFRESH_SECRET="your_production_refresh_secret_64_chars"
export REDIS_PASSWORD="your_production_redis_password"
export EMAIL_PASSWORD="your_production_email_password"
export AWS_SECRET_ACCESS_KEY="your_production_aws_secret"
```

### **Option B: Docker Secrets (Docker Swarm)**
```yaml
# docker-compose.prod.yml
secrets:
  db_password:
    external: true
  jwt_secret:
    external: true
```

### **Option C: Kubernetes Secrets**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: medtrack-secrets
type: Opaque
data:
  postgres-password: <base64-encoded-password>
  jwt-secret: <base64-encoded-secret>
```

### **Option D: Cloud Provider Secret Managers**
- **AWS**: AWS Secrets Manager / Parameter Store
- **Azure**: Azure Key Vault
- **GCP**: Google Secret Manager
- **DigitalOcean**: App Platform Environment Variables

## 3. **GitHub Repository Secrets** (For CI/CD)

### **Required GitHub Secrets**
Go to: `GitHub Repository → Settings → Secrets and variables → Actions`

Add these secrets:
```
DOCKER_REGISTRY_USERNAME=your_docker_hub_username
DOCKER_REGISTRY_PASSWORD=your_docker_hub_password
POSTGRES_PASSWORD=your_production_db_password
JWT_SECRET=your_production_jwt_secret
JWT_REFRESH_SECRET=your_production_refresh_secret
REDIS_PASSWORD=your_production_redis_password
EMAIL_PASSWORD=your_production_email_password
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
```

## 4. **How to Generate Secure Secrets**

### **Generate Strong Passwords/Secrets**
```bash
# Generate 32-character random string
openssl rand -base64 32

# Generate 64-character random string (recommended for production)
openssl rand -base64 48

# Generate UUID
uuidgen

# Generate using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# Generate using Python
python3 -c "import secrets; print(secrets.token_urlsafe(32))"
```

### **JWT Secret Requirements**
- **Minimum**: 32 characters
- **Recommended**: 64+ characters for production
- **Format**: Base64 encoded random bytes
- **Example**: `openssl rand -base64 48`

### **Database Password Requirements**
- **Minimum**: 16 characters
- **Include**: Uppercase, lowercase, numbers, special characters
- **Avoid**: Dictionary words, personal information
- **Example**: `Kp9#mN2$vL8@qR5!wX7&`

## 5. **Environment-Specific Configuration**

### **Development (.env)**
```bash
NODE_ENV=development
POSTGRES_PASSWORD=dev_password_123
JWT_SECRET=dev_jwt_secret_for_development_only
TYPEORM_SYNC=true
TYPEORM_LOGGING=true
```

### **Staging (.env.staging)**
```bash
NODE_ENV=staging
POSTGRES_PASSWORD=staging_secure_password_456
JWT_SECRET=staging_jwt_secret_64_chars_minimum_here
TYPEORM_SYNC=false
TYPEORM_LOGGING=false
```

### **Production (.env.production)**
```bash
NODE_ENV=production
POSTGRES_PASSWORD=production_ultra_secure_password_789
JWT_SECRET=production_jwt_secret_128_chars_ultra_secure_here
TYPEORM_SYNC=false
TYPEORM_LOGGING=false
DATABASE_SSL=true
```

## 6. **Security Best Practices**

### ✅ **DO**
- Use different secrets for each environment
- Rotate secrets regularly (every 90 days)
- Use strong, randomly generated passwords
- Store secrets in secure secret managers
- Use environment variables in production
- Audit secret access regularly

### ❌ **DON'T**
- Commit secrets to Git (ever!)
- Share secrets via email/chat
- Use the same secrets across environments
- Use weak or predictable passwords
- Store secrets in plain text files
- Log secrets in application logs

## 7. **Secret Rotation Strategy**

### **Quarterly Rotation (Recommended)**
1. Generate new secrets
2. Update secret managers/environment variables
3. Deploy applications with new secrets
4. Verify all services are working
5. Revoke old secrets

### **Emergency Rotation**
1. Immediately revoke compromised secrets
2. Generate new secrets
3. Emergency deployment
4. Audit access logs
5. Update security procedures

## 8. **Monitoring and Auditing**

### **What to Monitor**
- Failed authentication attempts
- Unusual access patterns
- Secret access logs
- Configuration changes

### **Alerting**
- Multiple failed login attempts
- Secret access from unusual locations
- Configuration drift detection
- Unauthorized secret access

---

## 🚨 **IMMEDIATE ACTION REQUIRED**

1. **Generate your actual secrets**:
   ```bash
   # Run the setup script to generate secrets
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

2. **Create your .env files**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

3. **Verify no secrets in Git**:
   ```bash
   git status
   # Ensure .env files are not staged
   ```

4. **Test with real secrets**:
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ./scripts/health-check.sh
   ```

Remember: **Security is not optional!** 🔐
